"""
混合模型预测系统 - HybridModel.py

基于设备数据变化特征的自适应混合预测模型系统
针对不同类型的设备采用最适合的预测算法：
- Stable设备：移动平均/线性回归
- Normal设备：SARIMA/SARIMAX
- Volatile设备：LSTM
- Expanded设备：指数平滑+趋势调整

作者：Augment AI
日期：2025-06-06
基于：LSTM-R.py
"""

import os
import pandas as pd
import numpy as np
import warnings
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from datetime import datetime, timedelta

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
except:
    print("警告：无法设置中文字体，可能导致中文显示不正确")

# 统计模型
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from statsmodels.tsa.seasonal import seasonal_decompose

# 机器学习模型
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.linear_model import LinearRegression
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.callbacks import EarlyStopping
from tensorflow.keras.optimizers import Adam

# 抑制警告
warnings.filterwarnings('ignore')

class HybridPredictor:
    """
    混合预测器类
    根据设备类型自动选择最适合的预测模型
    """

    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.device_types = {}

    def classify_device_type(self, data, device_id):
        """
        根据数据特征对设备进行分类
        """
        if len(data) < 4:
            return 'normal'  # 数据不足，默认为normal

        # 计算变异系数
        cv = data['实占率'].std() / data['实占率'].mean() if data['实占率'].mean() > 0 else 0

        # 检查是否有容量变化
        has_capacity_change = (data['容量变化'] > 0).any() if '容量变化' in data.columns else False

        # 分类逻辑
        if has_capacity_change:
            return 'expanded'
        elif cv > 0.2:
            return 'volatile'
        elif cv < 0.05:
            return 'stable'
        else:
            return 'normal'

    def moving_average_predict(self, data, window=4):
        """
        移动平均预测（适用于stable设备）
        """
        if len(data) < window:
            window = len(data)

        # 对实占率进行移动平均预测
        occupancy_ma = data['实占率'].rolling(window=window).mean().iloc[-1]

        # 对空闲端口数进行移动平均预测
        ports_ma = data['空闲端口数'].rolling(window=window).mean().iloc[-1]

        # 对潜在需求比进行移动平均预测
        demand_ma = data['潜在需求比'].rolling(window=window).mean().iloc[-1]

        return {
            'occupancy': occupancy_ma,
            'ports': int(round(ports_ma)),
            'demand': demand_ma
        }

    def linear_regression_predict(self, data):
        """
        线性回归预测（适用于stable设备的备选方案）
        """
        if len(data) < 3:
            return self.moving_average_predict(data)

        X = np.arange(len(data)).reshape(-1, 1)

        # 预测实占率
        y_occupancy = data['实占率'].values
        lr_occupancy = LinearRegression().fit(X, y_occupancy)
        pred_occupancy = lr_occupancy.predict([[len(data)]])[0]
        pred_occupancy = max(0, min(1, pred_occupancy))  # 限制在[0,1]范围内

        # 预测空闲端口数
        y_ports = data['空闲端口数'].values
        lr_ports = LinearRegression().fit(X, y_ports)
        pred_ports = lr_ports.predict([[len(data)]])[0]
        pred_ports = max(0, int(round(pred_ports)))

        # 预测潜在需求比
        y_demand = data['潜在需求比'].values
        lr_demand = LinearRegression().fit(X, y_demand)
        pred_demand = lr_demand.predict([[len(data)]])[0]
        pred_demand = max(0, pred_demand)

        return {
            'occupancy': pred_occupancy,
            'ports': pred_ports,
            'demand': pred_demand
        }

    def sarima_predict(self, data):
        """
        SARIMA预测（适用于normal设备）
        """
        try:
            if len(data) < 8:  # SARIMA需要足够的数据点
                return self.linear_regression_predict(data)

            predictions = {}

            # 预测实占率
            try:
                model_occupancy = SARIMAX(data['实占率'],
                                        order=(1, 1, 1),
                                        seasonal_order=(1, 1, 1, 4))
                fitted_occupancy = model_occupancy.fit(disp=False)
                pred_occupancy = fitted_occupancy.forecast(steps=1)[0]
                pred_occupancy = max(0, min(1, pred_occupancy))
                predictions['occupancy'] = pred_occupancy
            except:
                predictions['occupancy'] = data['实占率'].iloc[-1]

            # 预测空闲端口数
            try:
                model_ports = SARIMAX(data['空闲端口数'],
                                    order=(1, 1, 1),
                                    seasonal_order=(1, 1, 1, 4))
                fitted_ports = model_ports.fit(disp=False)
                pred_ports = fitted_ports.forecast(steps=1)[0]
                pred_ports = max(0, int(round(pred_ports)))
                predictions['ports'] = pred_ports
            except:
                predictions['ports'] = int(data['空闲端口数'].iloc[-1])

            # 预测潜在需求比
            try:
                model_demand = SARIMAX(data['潜在需求比'],
                                     order=(1, 1, 1),
                                     seasonal_order=(1, 1, 1, 4))
                fitted_demand = model_demand.fit(disp=False)
                pred_demand = fitted_demand.forecast(steps=1)[0]
                pred_demand = max(0, pred_demand)
                predictions['demand'] = pred_demand
            except:
                predictions['demand'] = data['潜在需求比'].iloc[-1]

            return predictions

        except Exception as e:
            print(f"SARIMA预测失败，使用线性回归备选方案: {e}")
            return self.linear_regression_predict(data)

    def exponential_smoothing_predict(self, data):
        """
        指数平滑预测（适用于expanded设备）
        """
        try:
            if len(data) < 4:
                return self.linear_regression_predict(data)

            predictions = {}

            # 预测实占率
            try:
                model_occupancy = ExponentialSmoothing(data['实占率'],
                                                     trend='add',
                                                     seasonal=None)
                fitted_occupancy = model_occupancy.fit()
                pred_occupancy = fitted_occupancy.forecast(steps=1)[0]
                pred_occupancy = max(0, min(1, pred_occupancy))
                predictions['occupancy'] = pred_occupancy
            except:
                predictions['occupancy'] = data['实占率'].iloc[-1]

            # 预测空闲端口数
            try:
                model_ports = ExponentialSmoothing(data['空闲端口数'],
                                                 trend='add',
                                                 seasonal=None)
                fitted_ports = model_ports.fit()
                pred_ports = fitted_ports.forecast(steps=1)[0]
                pred_ports = max(0, int(round(pred_ports)))
                predictions['ports'] = pred_ports
            except:
                predictions['ports'] = int(data['空闲端口数'].iloc[-1])

            # 预测潜在需求比
            try:
                model_demand = ExponentialSmoothing(data['潜在需求比'],
                                                  trend='add',
                                                  seasonal=None)
                fitted_demand = model_demand.fit()
                pred_demand = fitted_demand.forecast(steps=1)[0]
                pred_demand = max(0, pred_demand)
                predictions['demand'] = pred_demand
            except:
                predictions['demand'] = data['潜在需求比'].iloc[-1]

            return predictions

        except Exception as e:
            print(f"指数平滑预测失败，使用线性回归备选方案: {e}")
            return self.linear_regression_predict(data)

    def lstm_predict(self, data, device_id):
        """
        LSTM预测（适用于volatile设备）
        """
        try:
            if len(data) < 8:  # LSTM需要足够的数据点
                return self.linear_regression_predict(data)

            # 准备数据
            features = ['实占率', '空闲端口数', '潜在需求比']
            X = []
            y_occupancy = []
            y_ports = []
            y_demand = []

            seq_length = min(4, len(data) - 1)

            # 创建序列数据
            for i in range(len(data) - seq_length):
                X.append(data[features].iloc[i:i+seq_length].values)
                y_occupancy.append(data['实占率'].iloc[i+seq_length])
                y_ports.append(data['空闲端口数'].iloc[i+seq_length])
                y_demand.append(data['潜在需求比'].iloc[i+seq_length])

            if len(X) < 2:  # 数据不足
                return self.linear_regression_predict(data)

            X = np.array(X)
            y_occupancy = np.array(y_occupancy)
            y_ports = np.array(y_ports)
            y_demand = np.array(y_demand)

            # 数据标准化
            scaler_X = MinMaxScaler()
            scaler_y_occ = MinMaxScaler()
            scaler_y_ports = StandardScaler()
            scaler_y_demand = MinMaxScaler()

            X_scaled = scaler_X.fit_transform(X.reshape(-1, X.shape[-1])).reshape(X.shape)
            y_occ_scaled = scaler_y_occ.fit_transform(y_occupancy.reshape(-1, 1)).flatten()
            y_ports_scaled = scaler_y_ports.fit_transform(y_ports.reshape(-1, 1)).flatten()
            y_demand_scaled = scaler_y_demand.fit_transform(y_demand.reshape(-1, 1)).flatten()

            # 构建简化的LSTM模型
            model = Sequential([
                LSTM(32, activation='relu', input_shape=(seq_length, len(features))),
                Dropout(0.2),
                Dense(16, activation='relu'),
                Dense(3, activation='linear')  # 3个输出：实占率、空闲端口数、潜在需求比
            ])

            model.compile(optimizer=Adam(learning_rate=0.001), loss='mse')

            # 准备训练数据
            y_combined = np.column_stack([y_occ_scaled, y_ports_scaled, y_demand_scaled])

            # 训练模型
            model.fit(X_scaled, y_combined, epochs=50, batch_size=1, verbose=0)

            # 预测
            last_sequence = data[features].iloc[-seq_length:].values.reshape(1, seq_length, len(features))
            last_sequence_scaled = scaler_X.transform(last_sequence.reshape(-1, len(features))).reshape(last_sequence.shape)

            prediction_scaled = model.predict(last_sequence_scaled, verbose=0)[0]

            # 反标准化
            pred_occupancy = scaler_y_occ.inverse_transform([[prediction_scaled[0]]])[0][0]
            pred_ports = scaler_y_ports.inverse_transform([[prediction_scaled[1]]])[0][0]
            pred_demand = scaler_y_demand.inverse_transform([[prediction_scaled[2]]])[0][0]

            # 应用约束
            pred_occupancy = max(0, min(1, pred_occupancy))
            pred_ports = max(0, int(round(pred_ports)))
            pred_demand = max(0, pred_demand)

            return {
                'occupancy': pred_occupancy,
                'ports': pred_ports,
                'demand': pred_demand
            }

        except Exception as e:
            print(f"LSTM预测失败，使用线性回归备选方案: {e}")
            return self.linear_regression_predict(data)

    def predict(self, data, device_id):
        """
        主预测函数：根据设备类型自动选择最适合的预测模型
        """
        # 分类设备类型
        device_type = self.classify_device_type(data, device_id)
        self.device_types[device_id] = device_type

        print(f"设备 {device_id} 分类为: {device_type}")

        # 根据设备类型选择预测方法
        if device_type == 'stable':
            # 稳定设备：使用移动平均或线性回归
            if len(data) >= 6:
                return self.linear_regression_predict(data)
            else:
                return self.moving_average_predict(data)

        elif device_type == 'normal':
            # 正常设备：使用SARIMA
            return self.sarima_predict(data)

        elif device_type == 'volatile':
            # 波动设备：使用LSTM
            return self.lstm_predict(data, device_id)

        elif device_type == 'expanded':
            # 扩容设备：使用指数平滑
            return self.exponential_smoothing_predict(data)

        else:
            # 默认使用线性回归
            return self.linear_regression_predict(data)

# 数据加载与预处理函数（从LSTM-R.py复制并简化）
def load_and_preprocess_data(folder_path):
    """加载和预处理数据"""
    excel_files = [f for f in os.listdir(folder_path)
                   if f.startswith('三网小区预警_') and f.endswith('.xlsx')]
    all_data = pd.DataFrame()

    for file in excel_files:
        file_path = os.path.join(folder_path, file)
        df = pd.read_excel(file_path)
        keep_columns = [
            '所属设备', '小区入库时间', '覆盖的工程级的线路到达房间数',
            '分光器容量', '分光器空闲数', 'ftth终端数', 'now',
            '建筑群', 'address_desc', 'area', '分光器数'
        ]
        existing_columns = [col for col in keep_columns if col in df.columns]
        df = df[existing_columns]
        df['now'] = pd.to_datetime(df['now'], errors='coerce')
        df['小区入库时间'] = pd.to_datetime(df['小区入库时间'], errors='coerce')
        all_data = pd.concat([all_data, df], ignore_index=True)
    return all_data

def feature_engineering(all_data):
    """特征工程"""
    all_data['空闲率'] = all_data['分光器空闲数'] / all_data['分光器容量']
    all_data['潜在需求比'] = all_data['ftth终端数'] / (all_data['覆盖的工程级的线路到达房间数'])
    all_data['实占率'] = 1 - all_data['空闲率']
    all_data['空闲端口数'] = all_data['分光器空闲数']
    all_data['入库时间差_月'] = ((all_data['now'] - all_data['小区入库时间']).dt.days / 30).round()

    # 计算容量变化
    all_data = all_data.sort_values(['所属设备', 'now'])
    all_data['容量变化'] = all_data.groupby('所属设备')['分光器容量'].diff().fillna(0)

    return all_data

def aggregate_weekly_data(all_data):
    """按周聚合数据"""
    all_data['week'] = all_data['now'].dt.isocalendar().week
    all_data['周'] = all_data['now'].dt.isocalendar().year.astype(str) + '-' + \
                      all_data['week'].astype(str).str.zfill(2)

    agg_dict = {
        '实占率': 'mean',
        '潜在需求比': 'mean',
        '入库时间差_月': 'mean',
        '分光器容量': 'first',
        '容量变化': 'sum',
        '空闲端口数': 'mean',
        'ftth终端数': 'mean',
        '覆盖的工程级的线路到达房间数': 'first',
        '小区入库时间': 'first'
    }

    # 添加额外字段
    for col in ['建筑群', 'address_desc', 'area', '分光器数']:
        if col in all_data.columns:
            agg_dict[col] = 'first'

    weekly_data = all_data.groupby(['所属设备', '周']).agg(agg_dict).reset_index()
    return weekly_data

def calculate_risk_score(occupancy, ports, demand, capacity):
    """
    计算风险评分（从LSTM-R.py复制并简化）
    """
    reasons = []

    # 实占率评分
    if occupancy >= 0.97:
        occupancy_score = 100
        reasons.append("实占率极高（≥ 97%）")
    elif occupancy >= 0.93:
        occupancy_score = 90 + (occupancy - 0.93) * 250
        reasons.append("实占率非常高（≥ 93%）")
    elif occupancy >= 0.9:
        occupancy_score = 80 + (occupancy - 0.9) * 333
        reasons.append("实占率偏高（≥ 90%）")
    elif occupancy >= 0.8:
        occupancy_score = 60 + (occupancy - 0.8) * 200
        reasons.append("实占率较高（≥ 80%）")
    else:
        occupancy_score = occupancy * 75

    # 空闲端口数评分
    if ports <= 0:
        ports_score = 100
        reasons.append("无空闲端口")
    elif ports <= 1:
        ports_score = 95
        reasons.append("空闲端口数仅剩 1 个")
    elif ports <= 2:
        ports_score = 85
        reasons.append("空闲端口数仅剩 2 个")
    elif ports <= 3:
        ports_score = 70
        reasons.append("空闲端口数仅剩 3 个")
    elif ports <= 5:
        ports_score = 50
        reasons.append("空闲端口数较少（4-5个）")
    else:
        ports_score = max(0, 50 - (ports - 5) * 5)

    # 潜在需求比评分
    if demand >= 0.8:
        demand_score = 80
        reasons.append("潜在需求比很高（≥ 80%）")
    elif demand >= 0.6:
        demand_score = 60
        reasons.append("潜在需求比较高（≥ 60%）")
    elif demand >= 0.4:
        demand_score = 40
    else:
        demand_score = demand * 100

    # 加权总分
    weights = {'occupancy': 0.5, 'ports': 0.3, 'demand': 0.2}
    total_score = (
        weights['occupancy'] * occupancy_score +
        weights['ports'] * ports_score +
        weights['demand'] * demand_score
    )

    # 特殊规则
    if occupancy >= 0.95 and ports <= 2:
        total_score = max(total_score, 85)
        reasons.append("高实占率+低空闲端口数组合风险")
    elif occupancy >= 0.9 and ports <= 1:
        total_score = max(total_score, 80)
        reasons.append("高实占率+极低空闲端口数组合风险")

    # 确定风险级别
    if total_score >= 80:
        level = '紧急'
    elif total_score >= 60:
        level = '警告'
    elif total_score >= 40:
        level = '注意'
    else:
        level = '安全'

    return total_score, level, '; '.join(reasons) if reasons else '正常'

def generate_predictions(weekly_data, predictor):
    """
    生成所有设备的预测结果
    """
    results = []
    devices = weekly_data['所属设备'].unique()

    print(f"开始预测 {len(devices)} 个设备...")

    for i, device in enumerate(devices):
        if i % 100 == 0:
            print(f"已处理 {i}/{len(devices)} 个设备")

        device_data = weekly_data[weekly_data['所属设备'] == device].sort_values('周')

        if len(device_data) < 2:
            continue  # 数据不足，跳过

        # 获取最新数据
        latest_data = device_data.iloc[-1]

        # 进行预测
        try:
            prediction = predictor.predict(device_data, device)

            # 计算风险评分
            risk_score, risk_level, risk_reasons = calculate_risk_score(
                prediction['occupancy'],
                prediction['ports'],
                prediction['demand'],
                latest_data['分光器容量']
            )

            # 构建结果
            result = {
                '设备': device,
                '数据变化类型': predictor.device_types.get(device, 'unknown'),
                '当前实占率百分比': f"{latest_data['实占率']*100:.2f}%",
                '预测实占率百分比': f"{prediction['occupancy']*100:.2f}%",
                '当前空闲端口数': int(latest_data['空闲端口数']),
                '预测空闲端口数': prediction['ports'],
                '当前潜在需求比百分比': f"{latest_data['潜在需求比']*100:.2f}%",
                '预测潜在需求比百分比': f"{prediction['demand']*100:.2f}%",
                '预测风险评分': round(risk_score, 2),
                '预测风险级别': risk_level,
                '预测风险原因': risk_reasons,
                '分光器容量': int(latest_data['分光器容量']),
                '覆盖房间数': int(latest_data['覆盖的工程级的线路到达房间数']),
                'FTTH终端数': int(latest_data['ftth终端数']),
                '小区入库时间': latest_data['小区入库时间'],
                '入库时间差_月': round(latest_data['入库时间差_月'], 1) if pd.notna(latest_data['入库时间差_月']) else None
            }

            # 添加额外字段
            for col in ['建筑群', 'address_desc', 'area']:
                if col in latest_data:
                    result[col] = latest_data[col]

            results.append(result)

        except Exception as e:
            print(f"设备 {device} 预测失败: {e}")
            continue

    return results

def main():
    """主函数"""
    print("混合模型预测系统启动...")

    # 设置路径
    data_folder = 'D:\OBD\data_processed'
    results_dir = 'HybridModel_results'
    os.makedirs(results_dir, exist_ok=True)

    # 检查数据文件夹
    if not os.path.exists(data_folder):
        print(f"错误：数据文件夹 {data_folder} 不存在")
        return

    try:
        # 1. 数据加载与预处理
        print("加载数据...")
        all_data = load_and_preprocess_data(data_folder)
        print(f"加载了 {len(all_data)} 条数据记录")

        # 2. 特征工程
        print("进行特征工程...")
        all_data = feature_engineering(all_data)

        # 3. 数据聚合
        print("按周聚合数据...")
        weekly_data = aggregate_weekly_data(all_data)
        print(f"共有 {len(weekly_data['所属设备'].unique())} 个设备的周度数据")

        # 4. 初始化混合预测器
        print("初始化混合预测器...")
        predictor = HybridPredictor()

        # 5. 生成预测结果
        print("生成预测结果...")
        results = generate_predictions(weekly_data, predictor)

        # 6. 保存结果
        print("保存结果...")
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = os.path.join(results_dir, f'混合模型预测结果_{timestamp}.xlsx')

        results_df = pd.DataFrame(results)
        results_df.to_excel(output_file, index=False)
        print(f'预测结果已保存至 {output_file}')

        # 7. 输出统计信息
        print(f"\n=== 预测结果统计 ===")
        print(f"总预测设备数: {len(results)}")

        # 统计设备类型分布
        type_counts = results_df['数据变化类型'].value_counts()
        print("\n设备类型分布:")
        for device_type, count in type_counts.items():
            print(f"  {device_type}: {count} 个设备")

        # 统计风险级别分布
        risk_counts = results_df['预测风险级别'].value_counts()
        print("\n风险级别分布:")
        for level, count in risk_counts.items():
            print(f"  {level}: {count} 个设备")

        # 统计需要关注的设备
        high_risk = results_df[results_df['预测风险级别'].isin(['紧急', '警告'])]
        print(f"\n需要关注的设备数(紧急+警告): {len(high_risk)}")

        # 按设备类型统计风险级别
        type_risk = pd.crosstab(results_df['数据变化类型'], results_df['预测风险级别'])
        print("\n各类型设备风险级别统计:")
        print(type_risk)

        print("\n混合模型预测完成!")

    except Exception as e:
        print(f"运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()