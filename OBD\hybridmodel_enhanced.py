"""
增强版混合模型预测系统 - HybridModel_Enhanced.py

新增功能：
1. 训练集/测试集划分
2. 训练损失可视化
3. 预测准确性评估
4. 预测值与实际值的拟合度分析

基于：hybridmodel.py
"""

import os
import pandas as pd
import numpy as np
import warnings
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from datetime import datetime, timedelta
import seaborn as sns
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
except:
    print("警告：无法设置中文字体，可能导致中文显示不正确")

# 统计模型
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.holtwinters import ExponentialSmoothing

# 机器学习模型
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.linear_model import LinearRegression
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.callbacks import EarlyStopping, History
from tensorflow.keras.optimizers import Adam

# 抑制警告
warnings.filterwarnings('ignore')

class EnhancedHybridPredictor:
    """
    增强版混合预测器类
    包含训练过程监控和评估功能
    """

    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.device_types = {}
        self.training_histories = {}  # 存储训练历史
        self.validation_results = {}  # 存储验证结果

    def classify_device_type(self, data, device_id):
        """根据数据特征对设备进行分类"""
        if len(data) < 4:
            return 'normal'

        cv = data['实占率'].std() / data['实占率'].mean() if data['实占率'].mean() > 0 else 0
        has_capacity_change = (data['容量变化'] > 0).any() if '容量变化' in data.columns else False

        if has_capacity_change:
            return 'expanded'
        elif cv > 0.2:
            return 'volatile'
        elif cv < 0.05:
            return 'stable'
        else:
            return 'normal'

    def split_train_test(self, data, test_ratio=0.3):
        """
        按时间顺序划分训练集和测试集
        """
        if len(data) < 4:
            return data, data.iloc[-1:].copy()  # 数据不足时，测试集只取最后一条
        
        split_point = int(len(data) * (1 - test_ratio))
        split_point = max(2, split_point)  # 确保训练集至少有2条数据
        
        train_data = data.iloc[:split_point].copy()
        test_data = data.iloc[split_point:].copy()
        
        return train_data, test_data

    def evaluate_predictions(self, y_true, y_pred, metric_name):
        """
        评估预测结果
        """
        mse = mean_squared_error(y_true, y_pred)
        mae = mean_absolute_error(y_true, y_pred)
        rmse = np.sqrt(mse)
        
        # 避免除零错误
        if np.std(y_true) > 0:
            r2 = r2_score(y_true, y_pred)
        else:
            r2 = 0.0
        
        return {
            f'{metric_name}_MSE': mse,
            f'{metric_name}_MAE': mae,
            f'{metric_name}_RMSE': rmse,
            f'{metric_name}_R2': r2
        }

    def moving_average_predict_with_validation(self, train_data, test_data, window=4):
        """移动平均预测（带验证）"""
        if len(train_data) < window:
            window = len(train_data)

        # 训练阶段：计算移动平均
        occupancy_ma = train_data['实占率'].rolling(window=window).mean().iloc[-1]
        ports_ma = train_data['空闲端口数'].rolling(window=window).mean().iloc[-1]
        demand_ma = train_data['潜在需求比'].rolling(window=window).mean().iloc[-1]

        # 预测测试集
        test_predictions = []
        for i in range(len(test_data)):
            test_predictions.append({
                'occupancy': occupancy_ma,
                'ports': int(round(ports_ma)),
                'demand': demand_ma
            })

        # 评估
        if len(test_data) > 0:
            occupancy_metrics = self.evaluate_predictions(
                test_data['实占率'].values,
                [p['occupancy'] for p in test_predictions],
                'occupancy'
            )
            ports_metrics = self.evaluate_predictions(
                test_data['空闲端口数'].values,
                [p['ports'] for p in test_predictions],
                'ports'
            )
            demand_metrics = self.evaluate_predictions(
                test_data['潜在需求比'].values,
                [p['demand'] for p in test_predictions],
                'demand'
            )
            
            validation_metrics = {**occupancy_metrics, **ports_metrics, **demand_metrics}
        else:
            validation_metrics = {}

        return {
            'occupancy': occupancy_ma,
            'ports': int(round(ports_ma)),
            'demand': demand_ma
        }, validation_metrics, test_predictions

    def linear_regression_predict_with_validation(self, train_data, test_data):
        """线性回归预测（带验证）"""
        if len(train_data) < 3:
            return self.moving_average_predict_with_validation(train_data, test_data)

        X_train = np.arange(len(train_data)).reshape(-1, 1)
        
        # 训练模型
        lr_occupancy = LinearRegression().fit(X_train, train_data['实占率'].values)
        lr_ports = LinearRegression().fit(X_train, train_data['空闲端口数'].values)
        lr_demand = LinearRegression().fit(X_train, train_data['潜在需求比'].values)

        # 预测下一个时间点
        next_time = len(train_data)
        pred_occupancy = lr_occupancy.predict([[next_time]])[0]
        pred_occupancy = max(0, min(1, pred_occupancy))
        
        pred_ports = lr_ports.predict([[next_time]])[0]
        pred_ports = max(0, int(round(pred_ports)))
        
        pred_demand = lr_demand.predict([[next_time]])[0]
        pred_demand = max(0, pred_demand)

        # 预测测试集
        test_predictions = []
        for i in range(len(test_data)):
            test_time = len(train_data) + i
            test_occ = max(0, min(1, lr_occupancy.predict([[test_time]])[0]))
            test_ports = max(0, int(round(lr_ports.predict([[test_time]])[0])))
            test_demand = max(0, lr_demand.predict([[test_time]])[0])
            
            test_predictions.append({
                'occupancy': test_occ,
                'ports': test_ports,
                'demand': test_demand
            })

        # 评估
        validation_metrics = {}
        if len(test_data) > 0:
            occupancy_metrics = self.evaluate_predictions(
                test_data['实占率'].values,
                [p['occupancy'] for p in test_predictions],
                'occupancy'
            )
            ports_metrics = self.evaluate_predictions(
                test_data['空闲端口数'].values,
                [p['ports'] for p in test_predictions],
                'ports'
            )
            demand_metrics = self.evaluate_predictions(
                test_data['潜在需求比'].values,
                [p['demand'] for p in test_predictions],
                'demand'
            )
            
            validation_metrics = {**occupancy_metrics, **ports_metrics, **demand_metrics}

        return {
            'occupancy': pred_occupancy,
            'ports': pred_ports,
            'demand': pred_demand
        }, validation_metrics, test_predictions

    def lstm_predict_with_validation(self, train_data, test_data, device_id):
        """LSTM预测（带验证和训练历史记录）"""
        try:
            if len(train_data) < 6:
                return self.linear_regression_predict_with_validation(train_data, test_data)

            # 准备数据
            features = ['实占率', '空闲端口数', '潜在需求比']
            all_data = pd.concat([train_data, test_data])
            
            X = []
            y_occupancy = []
            y_ports = []
            y_demand = []

            seq_length = min(3, len(train_data) - 1)

            # 创建序列数据
            for i in range(len(all_data) - seq_length):
                X.append(all_data[features].iloc[i:i+seq_length].values)
                y_occupancy.append(all_data['实占率'].iloc[i+seq_length])
                y_ports.append(all_data['空闲端口数'].iloc[i+seq_length])
                y_demand.append(all_data['潜在需求比'].iloc[i+seq_length])

            if len(X) < 2:
                return self.linear_regression_predict_with_validation(train_data, test_data)

            X = np.array(X)
            y_occupancy = np.array(y_occupancy)
            y_ports = np.array(y_ports)
            y_demand = np.array(y_demand)

            # 划分训练和验证数据
            train_size = len(train_data) - seq_length
            X_train, X_val = X[:train_size], X[train_size:]
            y_occ_train, y_occ_val = y_occupancy[:train_size], y_occupancy[train_size:]
            y_ports_train, y_ports_val = y_ports[:train_size], y_ports[train_size:]
            y_demand_train, y_demand_val = y_demand[:train_size], y_demand[train_size:]

            # 数据标准化
            scaler_X = MinMaxScaler()
            scaler_y_occ = MinMaxScaler()
            scaler_y_ports = StandardScaler()
            scaler_y_demand = MinMaxScaler()

            X_train_scaled = scaler_X.fit_transform(X_train.reshape(-1, X_train.shape[-1])).reshape(X_train.shape)
            X_val_scaled = scaler_X.transform(X_val.reshape(-1, X_val.shape[-1])).reshape(X_val.shape)
            
            y_occ_train_scaled = scaler_y_occ.fit_transform(y_occ_train.reshape(-1, 1)).flatten()
            y_ports_train_scaled = scaler_y_ports.fit_transform(y_ports_train.reshape(-1, 1)).flatten()
            y_demand_train_scaled = scaler_y_demand.fit_transform(y_demand_train.reshape(-1, 1)).flatten()

            # 构建LSTM模型
            model = Sequential([
                LSTM(16, activation='relu', input_shape=(seq_length, len(features))),
                Dropout(0.2),
                Dense(8, activation='relu'),
                Dense(3, activation='linear')
            ])

            model.compile(optimizer=Adam(learning_rate=0.001), loss='mse')

            # 准备训练数据
            y_train_combined = np.column_stack([y_occ_train_scaled, y_ports_train_scaled, y_demand_train_scaled])
            
            # 训练模型并记录历史
            history = model.fit(
                X_train_scaled, y_train_combined, 
                epochs=30, 
                batch_size=1, 
                verbose=0,
                validation_split=0.2 if len(X_train_scaled) > 5 else 0
            )
            
            # 保存训练历史
            self.training_histories[device_id] = history.history

            # 预测
            last_sequence = train_data[features].iloc[-seq_length:].values.reshape(1, seq_length, len(features))
            last_sequence_scaled = scaler_X.transform(last_sequence.reshape(-1, len(features))).reshape(last_sequence.shape)

            prediction_scaled = model.predict(last_sequence_scaled, verbose=0)[0]

            # 反标准化
            pred_occupancy = scaler_y_occ.inverse_transform([[prediction_scaled[0]]])[0][0]
            pred_ports = scaler_y_ports.inverse_transform([[prediction_scaled[1]]])[0][0]
            pred_demand = scaler_y_demand.inverse_transform([[prediction_scaled[2]]])[0][0]

            # 应用约束
            pred_occupancy = max(0, min(1, pred_occupancy))
            pred_ports = max(0, int(round(pred_ports)))
            pred_demand = max(0, pred_demand)

            # 预测测试集
            test_predictions = []
            if len(X_val) > 0:
                val_predictions_scaled = model.predict(X_val_scaled, verbose=0)
                
                for i, pred_scaled in enumerate(val_predictions_scaled):
                    test_occ = scaler_y_occ.inverse_transform([[pred_scaled[0]]])[0][0]
                    test_ports = scaler_y_ports.inverse_transform([[pred_scaled[1]]])[0][0]
                    test_demand = scaler_y_demand.inverse_transform([[pred_scaled[2]]])[0][0]
                    
                    test_predictions.append({
                        'occupancy': max(0, min(1, test_occ)),
                        'ports': max(0, int(round(test_ports))),
                        'demand': max(0, test_demand)
                    })

            # 评估
            validation_metrics = {}
            if len(test_predictions) > 0 and len(test_data) > 0:
                occupancy_metrics = self.evaluate_predictions(
                    test_data['实占率'].values,
                    [p['occupancy'] for p in test_predictions],
                    'occupancy'
                )
                ports_metrics = self.evaluate_predictions(
                    test_data['空闲端口数'].values,
                    [p['ports'] for p in test_predictions],
                    'ports'
                )
                demand_metrics = self.evaluate_predictions(
                    test_data['潜在需求比'].values,
                    [p['demand'] for p in test_predictions],
                    'demand'
                )
                
                validation_metrics = {**occupancy_metrics, **ports_metrics, **demand_metrics}

            return {
                'occupancy': pred_occupancy,
                'ports': pred_ports,
                'demand': pred_demand
            }, validation_metrics, test_predictions

        except Exception as e:
            print(f"LSTM预测失败，使用线性回归备选方案: {e}")
            return self.linear_regression_predict_with_validation(train_data, test_data)

    def predict_with_validation(self, data, device_id):
        """
        主预测函数：包含训练集/测试集划分和验证
        """
        # 分类设备类型
        device_type = self.classify_device_type(data, device_id)
        self.device_types[device_id] = device_type

        print(f"设备 {device_id} 分类为: {device_type}")

        # 划分训练集和测试集
        train_data, test_data = self.split_train_test(data)
        print(f"  训练集: {len(train_data)} 条, 测试集: {len(test_data)} 条")

        # 根据设备类型选择预测方法
        if device_type == 'stable':
            if len(train_data) >= 4:
                prediction, validation_metrics, test_predictions = self.linear_regression_predict_with_validation(train_data, test_data)
            else:
                prediction, validation_metrics, test_predictions = self.moving_average_predict_with_validation(train_data, test_data)

        elif device_type == 'volatile':
            prediction, validation_metrics, test_predictions = self.lstm_predict_with_validation(train_data, test_data, device_id)

        else:  # normal, expanded, 或其他
            prediction, validation_metrics, test_predictions = self.linear_regression_predict_with_validation(train_data, test_data)

        # 保存验证结果
        self.validation_results[device_id] = {
            'device_type': device_type,
            'train_size': len(train_data),
            'test_size': len(test_data),
            'metrics': validation_metrics,
            'test_predictions': test_predictions,
            'test_actual': test_data[['实占率', '空闲端口数', '潜在需求比']].to_dict('records') if len(test_data) > 0 else []
        }

        return prediction

    def plot_training_history(self, device_id, save_dir='visualization_results'):
        """
        绘制训练历史（仅适用于LSTM模型）
        """
        if device_id not in self.training_histories:
            print(f"设备 {device_id} 没有训练历史记录")
            return

        os.makedirs(save_dir, exist_ok=True)

        history = self.training_histories[device_id]

        plt.figure(figsize=(12, 4))

        # 训练损失
        plt.subplot(1, 2, 1)
        plt.plot(history['loss'], label='训练损失')
        if 'val_loss' in history:
            plt.plot(history['val_loss'], label='验证损失')
        plt.title(f'设备 {device_id} - 训练损失')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        plt.grid(True)

        # 如果有验证损失，绘制损失对比
        if 'val_loss' in history:
            plt.subplot(1, 2, 2)
            plt.plot(history['loss'], label='训练损失')
            plt.plot(history['val_loss'], label='验证损失')
            plt.title(f'设备 {device_id} - 损失对比')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.legend()
            plt.grid(True)

        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, f'训练历史_{device_id}.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def plot_prediction_comparison(self, device_id, save_dir='visualization_results'):
        """
        绘制预测值与实际值的对比
        """
        if device_id not in self.validation_results:
            print(f"设备 {device_id} 没有验证结果")
            return

        os.makedirs(save_dir, exist_ok=True)

        validation_data = self.validation_results[device_id]
        test_predictions = validation_data['test_predictions']
        test_actual = validation_data['test_actual']

        if len(test_predictions) == 0 or len(test_actual) == 0:
            print(f"设备 {device_id} 没有足够的测试数据进行对比")
            return

        # 提取数据
        actual_occupancy = [item['实占率'] for item in test_actual]
        pred_occupancy = [item['occupancy'] for item in test_predictions]

        actual_ports = [item['空闲端口数'] for item in test_actual]
        pred_ports = [item['ports'] for item in test_predictions]

        actual_demand = [item['潜在需求比'] for item in test_actual]
        pred_demand = [item['demand'] for item in test_predictions]

        # 绘制对比图
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # 实占率对比
        axes[0, 0].plot(actual_occupancy, 'o-', label='实际值', color='blue')
        axes[0, 0].plot(pred_occupancy, 's-', label='预测值', color='red')
        axes[0, 0].set_title(f'设备 {device_id} - 实占率预测对比')
        axes[0, 0].set_ylabel('实占率')
        axes[0, 0].legend()
        axes[0, 0].grid(True)

        # 空闲端口数对比
        axes[0, 1].plot(actual_ports, 'o-', label='实际值', color='blue')
        axes[0, 1].plot(pred_ports, 's-', label='预测值', color='red')
        axes[0, 1].set_title(f'设备 {device_id} - 空闲端口数预测对比')
        axes[0, 1].set_ylabel('空闲端口数')
        axes[0, 1].legend()
        axes[0, 1].grid(True)

        # 潜在需求比对比
        axes[1, 0].plot(actual_demand, 'o-', label='实际值', color='blue')
        axes[1, 0].plot(pred_demand, 's-', label='预测值', color='red')
        axes[1, 0].set_title(f'设备 {device_id} - 潜在需求比预测对比')
        axes[1, 0].set_ylabel('潜在需求比')
        axes[1, 0].legend()
        axes[1, 0].grid(True)

        # 散点图：预测值 vs 实际值
        axes[1, 1].scatter(actual_occupancy, pred_occupancy, alpha=0.7, label='实占率')
        if len(actual_ports) > 0 and max(actual_ports) > 0:
            # 标准化端口数以便在同一图中显示
            norm_actual_ports = np.array(actual_ports) / max(actual_ports)
            norm_pred_ports = np.array(pred_ports) / max(pred_ports) if max(pred_ports) > 0 else np.array(pred_ports)
            axes[1, 1].scatter(norm_actual_ports, norm_pred_ports, alpha=0.7, label='空闲端口数(标准化)')
        axes[1, 1].scatter(actual_demand, pred_demand, alpha=0.7, label='潜在需求比')
        axes[1, 1].plot([0, 1], [0, 1], 'k--', alpha=0.5, label='完美预测线')
        axes[1, 1].set_xlabel('实际值')
        axes[1, 1].set_ylabel('预测值')
        axes[1, 1].set_title(f'设备 {device_id} - 预测准确性散点图')
        axes[1, 1].legend()
        axes[1, 1].grid(True)

        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, f'预测对比_{device_id}.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def generate_validation_report(self, save_dir='visualization_results'):
        """
        生成验证报告
        """
        os.makedirs(save_dir, exist_ok=True)

        report_data = []

        for device_id, validation_data in self.validation_results.items():
            metrics = validation_data['metrics']
            device_type = validation_data['device_type']
            train_size = validation_data['train_size']
            test_size = validation_data['test_size']

            report_row = {
                '设备ID': device_id,
                '设备类型': device_type,
                '训练集大小': train_size,
                '测试集大小': test_size,
            }

            # 添加各项指标
            for metric_name, metric_value in metrics.items():
                report_row[metric_name] = metric_value

            report_data.append(report_row)

        # 保存详细报告
        report_df = pd.DataFrame(report_data)
        report_file = os.path.join(save_dir, f'验证报告_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx')
        report_df.to_excel(report_file, index=False)

        # 生成汇总统计
        summary_stats = {}

        # 按设备类型统计
        type_stats = report_df.groupby('设备类型').agg({
            '训练集大小': ['mean', 'std'],
            '测试集大小': ['mean', 'std']
        }).round(2)

        # 计算各指标的平均值
        metric_columns = [col for col in report_df.columns if any(metric in col for metric in ['MSE', 'MAE', 'RMSE', 'R2'])]

        if metric_columns:
            metric_stats = report_df.groupby('设备类型')[metric_columns].mean().round(4)

            print("\n=== 验证结果汇总 ===")
            print(f"总设备数: {len(report_df)}")
            print(f"验证报告已保存至: {report_file}")

            print("\n各设备类型数据统计:")
            print(type_stats)

            print("\n各设备类型预测性能:")
            print(metric_stats)

            # 找出表现最好和最差的设备
            if 'occupancy_R2' in report_df.columns:
                best_device = report_df.loc[report_df['occupancy_R2'].idxmax()]
                worst_device = report_df.loc[report_df['occupancy_R2'].idxmin()]

                print(f"\n实占率预测表现最好的设备: {best_device['设备ID']} (R2: {best_device['occupancy_R2']:.4f})")
                print(f"实占率预测表现最差的设备: {worst_device['设备ID']} (R2: {worst_device['occupancy_R2']:.4f})")

        return report_df


# 数据加载与预处理函数（从原始hybridmodel.py复制）
def load_and_preprocess_data(folder_path):
    """加载和预处理数据"""
    excel_files = [f for f in os.listdir(folder_path)
                   if f.startswith('三网小区预警_') and f.endswith('.xlsx')]
    all_data = pd.DataFrame()

    for file in excel_files:
        file_path = os.path.join(folder_path, file)
        df = pd.read_excel(file_path)
        keep_columns = [
            '所属设备', '小区入库时间', '覆盖的工程级的线路到达房间数',
            '分光器容量', '分光器空闲数', 'ftth终端数', 'now',
            '建筑群', 'address_desc', 'area', '分光器数'
        ]
        existing_columns = [col for col in keep_columns if col in df.columns]
        df = df[existing_columns]
        df['now'] = pd.to_datetime(df['now'], errors='coerce')
        df['小区入库时间'] = pd.to_datetime(df['小区入库时间'], errors='coerce')
        all_data = pd.concat([all_data, df], ignore_index=True)
    return all_data

def feature_engineering(all_data):
    """特征工程"""
    all_data['空闲率'] = all_data['分光器空闲数'] / all_data['分光器容量']
    all_data['潜在需求比'] = all_data['ftth终端数'] / (all_data['覆盖的工程级的线路到达房间数'])
    all_data['实占率'] = 1 - all_data['空闲率']
    all_data['空闲端口数'] = all_data['分光器空闲数']
    all_data['入库时间差_月'] = ((all_data['now'] - all_data['小区入库时间']).dt.days / 30).round()

    # 计算容量变化
    all_data = all_data.sort_values(['所属设备', 'now'])
    all_data['容量变化'] = all_data.groupby('所属设备')['分光器容量'].diff().fillna(0)

    return all_data

def aggregate_weekly_data(all_data):
    """按周聚合数据"""
    all_data['week'] = all_data['now'].dt.isocalendar().week
    all_data['周'] = all_data['now'].dt.isocalendar().year.astype(str) + '-' + \
                      all_data['week'].astype(str).str.zfill(2)

    agg_dict = {
        '实占率': 'mean',
        '潜在需求比': 'mean',
        '入库时间差_月': 'mean',
        '分光器容量': 'first',
        '容量变化': 'sum',
        '空闲端口数': 'mean',
        'ftth终端数': 'mean',
        '覆盖的工程级的线路到达房间数': 'first',
        '小区入库时间': 'first'
    }

    # 添加额外字段
    for col in ['建筑群', 'address_desc', 'area', '分光器数']:
        if col in all_data.columns:
            agg_dict[col] = 'first'

    weekly_data = all_data.groupby(['所属设备', '周']).agg(agg_dict).reset_index()
    return weekly_data

def calculate_risk_score(occupancy, ports, demand, capacity):
    """计算风险评分"""
    reasons = []

    # 实占率评分
    if occupancy >= 0.97:
        occupancy_score = 100
        reasons.append("实占率极高（≥ 97%）")
    elif occupancy >= 0.93:
        occupancy_score = 90 + (occupancy - 0.93) * 250
        reasons.append("实占率非常高（≥ 93%）")
    elif occupancy >= 0.9:
        occupancy_score = 80 + (occupancy - 0.9) * 333
        reasons.append("实占率偏高（≥ 90%）")
    elif occupancy >= 0.8:
        occupancy_score = 60 + (occupancy - 0.8) * 200
        reasons.append("实占率较高（≥ 80%）")
    else:
        occupancy_score = occupancy * 75

    # 空闲端口数评分
    if ports <= 0:
        ports_score = 100
        reasons.append("无空闲端口")
    elif ports <= 1:
        ports_score = 95
        reasons.append("空闲端口数仅剩 1 个")
    elif ports <= 2:
        ports_score = 85
        reasons.append("空闲端口数仅剩 2 个")
    elif ports <= 3:
        ports_score = 70
        reasons.append("空闲端口数仅剩 3 个")
    elif ports <= 5:
        ports_score = 50
        reasons.append("空闲端口数较少（4-5个）")
    else:
        ports_score = max(0, 50 - (ports - 5) * 5)

    # 潜在需求比评分
    if demand >= 0.8:
        demand_score = 80
        reasons.append("潜在需求比很高（≥ 80%）")
    elif demand >= 0.6:
        demand_score = 60
        reasons.append("潜在需求比较高（≥ 60%）")
    elif demand >= 0.4:
        demand_score = 40
    else:
        demand_score = demand * 100

    # 加权总分
    weights = {'occupancy': 0.5, 'ports': 0.3, 'demand': 0.2}
    total_score = (
        weights['occupancy'] * occupancy_score +
        weights['ports'] * ports_score +
        weights['demand'] * demand_score
    )

    # 特殊规则
    if occupancy >= 0.95 and ports <= 2:
        total_score = max(total_score, 85)
        reasons.append("高实占率+低空闲端口数组合风险")
    elif occupancy >= 0.9 and ports <= 1:
        total_score = max(total_score, 80)
        reasons.append("高实占率+极低空闲端口数组合风险")

    # 确定风险级别
    if total_score >= 80:
        level = '紧急'
    elif total_score >= 60:
        level = '警告'
    elif total_score >= 40:
        level = '注意'
    else:
        level = '安全'

    return total_score, level, '; '.join(reasons) if reasons else '正常'


def generate_enhanced_predictions(weekly_data, predictor, max_devices=None):
    """
    生成增强版预测结果（包含验证）
    """
    results = []
    devices = weekly_data['所属设备'].unique()

    if max_devices:
        devices = devices[:max_devices]  # 限制设备数量用于测试

    print(f"开始预测 {len(devices)} 个设备...")

    for i, device in enumerate(devices):
        if i % 50 == 0:
            print(f"已处理 {i}/{len(devices)} 个设备")

        device_data = weekly_data[weekly_data['所属设备'] == device].sort_values('周')

        if len(device_data) < 3:  # 需要至少3条数据进行训练和测试
            continue

        # 获取最新数据
        latest_data = device_data.iloc[-1]

        # 进行预测（包含验证）
        try:
            prediction = predictor.predict_with_validation(device_data, device)

            # 计算风险评分
            risk_score, risk_level, risk_reasons = calculate_risk_score(
                prediction['occupancy'],
                prediction['ports'],
                prediction['demand'],
                latest_data['分光器容量']
            )

            # 构建结果
            result = {
                '设备': device,
                '数据变化类型': predictor.device_types.get(device, 'unknown'),
                '当前实占率百分比': f"{latest_data['实占率']*100:.2f}%",
                '预测实占率百分比': f"{prediction['occupancy']*100:.2f}%",
                '当前空闲端口数': int(latest_data['空闲端口数']),
                '预测空闲端口数': prediction['ports'],
                '当前潜在需求比百分比': f"{latest_data['潜在需求比']*100:.2f}%",
                '预测潜在需求比百分比': f"{prediction['demand']*100:.2f}%",
                '预测风险评分': round(risk_score, 2),
                '预测风险级别': risk_level,
                '预测风险原因': risk_reasons,
                '分光器容量': int(latest_data['分光器容量']),
                '覆盖房间数': int(latest_data['覆盖的工程级的线路到达房间数']),
                'FTTH终端数': int(latest_data['ftth终端数']),
                '小区入库时间': latest_data['小区入库时间'],
                '入库时间差_月': round(latest_data['入库时间差_月'], 1) if pd.notna(latest_data['入库时间差_月']) else None
            }

            # 添加验证指标
            if device in predictor.validation_results:
                validation_data = predictor.validation_results[device]
                metrics = validation_data['metrics']

                # 添加主要验证指标
                for metric_name in ['occupancy_R2', 'occupancy_MAE', 'ports_R2', 'ports_MAE']:
                    if metric_name in metrics:
                        result[f'验证_{metric_name}'] = round(metrics[metric_name], 4)

            # 添加额外字段
            for col in ['建筑群', 'address_desc', 'area']:
                if col in latest_data:
                    result[col] = latest_data[col]

            results.append(result)

        except Exception as e:
            print(f"设备 {device} 预测失败: {e}")
            continue

    return results


def main():
    """主函数"""
    print("增强版混合模型预测系统启动...")

    # 设置路径
    data_folder = 'D:/OBD/data_processed'
    results_dir = 'HybridModel_Enhanced_results'
    visualization_dir = 'visualization_results'

    os.makedirs(results_dir, exist_ok=True)
    os.makedirs(visualization_dir, exist_ok=True)

    # 检查数据文件夹
    if not os.path.exists(data_folder):
        print(f"错误：数据文件夹 {data_folder} 不存在")
        return

    try:
        # 1. 数据加载与预处理
        print("加载数据...")
        all_data = load_and_preprocess_data(data_folder)
        print(f"加载了 {len(all_data)} 条数据记录")

        # 2. 特征工程
        print("进行特征工程...")
        all_data = feature_engineering(all_data)

        # 3. 数据聚合
        print("按周聚合数据...")
        weekly_data = aggregate_weekly_data(all_data)
        print(f"共有 {len(weekly_data['所属设备'].unique())} 个设备的周度数据")

        # 4. 初始化增强版混合预测器
        print("初始化增强版混合预测器...")
        predictor = EnhancedHybridPredictor()

        # 5. 生成预测结果（限制设备数量用于演示）
        print("生成预测结果...")
        max_devices = 100  # 限制设备数量，可以根据需要调整
        results = generate_enhanced_predictions(weekly_data, predictor, max_devices)

        # 6. 保存结果
        print("保存结果...")
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = os.path.join(results_dir, f'增强版混合模型预测结果_{timestamp}.xlsx')

        results_df = pd.DataFrame(results)
        results_df.to_excel(output_file, index=False)
        print(f'预测结果已保存至 {output_file}')

        # 7. 生成验证报告
        print("生成验证报告...")
        validation_report = predictor.generate_validation_report(visualization_dir)

        # 8. 生成可视化图表（选择几个代表性设备）
        print("生成可视化图表...")
        sample_devices = list(predictor.validation_results.keys())[:5]  # 选择前5个设备

        for device_id in sample_devices:
            try:
                # 绘制训练历史（如果有）
                predictor.plot_training_history(device_id, visualization_dir)

                # 绘制预测对比
                predictor.plot_prediction_comparison(device_id, visualization_dir)

            except Exception as e:
                print(f"为设备 {device_id} 生成图表时出错: {e}")

        # 9. 输出统计信息
        print(f"\n=== 增强版预测结果统计 ===")
        print(f"总预测设备数: {len(results)}")

        # 统计设备类型分布
        type_counts = results_df['数据变化类型'].value_counts()
        print("\n设备类型分布:")
        for device_type, count in type_counts.items():
            print(f"  {device_type}: {count} 个设备")

        # 统计风险级别分布
        risk_counts = results_df['预测风险级别'].value_counts()
        print("\n风险级别分布:")
        for level, count in risk_counts.items():
            print(f"  {level}: {count} 个设备")

        # 统计需要关注的设备
        high_risk = results_df[results_df['预测风险级别'].isin(['紧急', '警告'])]
        print(f"\n需要关注的设备数(紧急+警告): {len(high_risk)}")

        print(f"\n可视化结果已保存至: {visualization_dir}")
        print("\n增强版混合模型预测完成!")

    except Exception as e:
        print(f"运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
